{
    'name': 'BSIC Requests Management',
    'version': '1.0.4',
    'summary': 'Manage IT and other requests with approval workflow - Enhanced Performance, Security & Activity Logging',
    'description': """
        This module manages various types of requests with a multi-stage approval workflow:
        - Password reset requests
        - Device usage extension requests
        - USB storage usage requests
        - And more...
    """,
    'category': 'Human Resources',
    'author': 'BSIC',
    'depends': ['base', 'hr', 'mail'],
    # Find this section in your manifest file
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'data/sequence.xml',
        'data/request_type_data.xml',
        'data/technical_category_data.xml',
        'data/stationery_item_data.xml',

        'views/request_type_action.xml',
        'views/stationery_item_action.xml',
        'views/request_views.xml',
        'views/request_type_views.xml',
        # 'views/permission_request_views.xml', - removed, now using request_views.xml
        'views/technical_category_views.xml',
        'views/technical_request_views_new.xml',
        'views/technical_support_menu.xml',
        'views/password_reset_request_views.xml',
        'views/usb_request_views.xml',
        'views/extension_request_views.xml',
        'views/permission_request_views_new.xml',
        'views/email_request_views.xml',
        'views/authorization_delegation_request_views.xml',
        'views/free_entry_request_views.xml',
        'views/stationery_request_views.xml',

        'views/receipt_confirmation_wizard_view.xml',
        'views/request_activity_log_views.xml',
        'views/menu_views.xml',
        # Uncomment the line below if you want to use the request_menu.xml file
        # 'views/request_menu.xml',

        # Demo data files - will be loaded automatically when module is installed
        'data/demo/hr_employee_demo.xml',
        'data/demo/request_demo_simple.xml',
        'data/demo/request_demo_additional.xml',
    ],
    'demo': [],
    'installable': True,
    'application': True,
    'auto_install': False,
}