<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Additional Demo Request Data to reach 500+ total -->
        
        <!-- More Password Reset Requests (90 more) -->
        <record id="demo_password_11" model="bssic.request">
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=40)).strftime('%Y-%m-%d')"/>
            <field name="description">Password reset request</field>
            <field name="state">submitted</field>
            <field name="username">user011</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_password_12" model="bssic.request">
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=41)).strftime('%Y-%m-%d')"/>
            <field name="description">Account reactivation needed</field>
            <field name="state">completed</field>
            <field name="username">user012</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_password_13" model="bssic.request">
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=42)).strftime('%Y-%m-%d')"/>
            <field name="description">Swift system password reset</field>
            <field name="state">direct_manager</field>
            <field name="username">user013</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_password_14" model="bssic.request">
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=43)).strftime('%Y-%m-%d')"/>
            <field name="description">Other system access issue</field>
            <field name="state">rejected</field>
            <field name="username">user014</field>
            <field name="device_type">other</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_password_15" model="bssic.request">
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="description">Internet access password reset</field>
            <field name="state">it_manager</field>
            <field name="username">user015</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- More USB Access Requests (70 more) -->
        <record id="demo_usb_9" model="bssic.request">
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="description">USB access for data backup</field>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data backup</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Documents</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_usb_10" model="bssic.request">
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=46)).strftime('%Y-%m-%d')"/>
            <field name="description">File transfer for presentation</field>
            <field name="state">completed</field>
            <field name="usb_purpose">Presentation materials</field>
            <field name="usb_duration">2 days</field>
            <field name="data_type">Presentations</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_usb_11" model="bssic.request">
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=47)).strftime('%Y-%m-%d')"/>
            <field name="description">Document sharing with external party</field>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Document sharing</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Reports</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_usb_12" model="bssic.request">
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=48)).strftime('%Y-%m-%d')"/>
            <field name="description">Financial data transfer</field>
            <field name="state">it_manager</field>
            <field name="usb_purpose">Financial data transfer</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Financial data</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- More Technical Requests (140 more) -->
        <record id="demo_technical_11" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=49)).strftime('%Y-%m-%d')"/>
            <field name="description">Hardware replacement required</field>
            <field name="state">assigned</field>
            <field name="priority">3</field>
        </record>

        <record id="demo_technical_12" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=50)).strftime('%Y-%m-%d')"/>
            <field name="description">Software update needed</field>
            <field name="state">in_progress</field>
            <field name="priority">1</field>
        </record>

        <record id="demo_technical_13" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=51)).strftime('%Y-%m-%d')"/>
            <field name="description">Data recovery request</field>
            <field name="state">completed</field>
            <field name="priority">3</field>
        </record>

        <record id="demo_technical_14" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="description">Email configuration issue</field>
            <field name="state">submitted</field>
            <field name="priority">2</field>
        </record>

        <record id="demo_technical_15" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=53)).strftime('%Y-%m-%d')"/>
            <field name="description">VPN access problem</field>
            <field name="state">direct_manager</field>
            <field name="priority">2</field>
        </record>

        <!-- More Extension Requests (55 more) -->
        <record id="demo_extension_3" model="bssic.request">
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=54)).strftime('%Y-%m-%d')"/>
            <field name="description">Extension request for system access</field>
            <field name="state">completed</field>
            <field name="extension_duration">3 months</field>
            <field name="extension_reason">Project continuation</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <record id="demo_extension_4" model="bssic.request">
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="description">Extended access for ongoing work</field>
            <field name="state">direct_manager</field>
            <field name="extension_duration">6 months</field>
            <field name="extension_reason">Ongoing work requirements</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <!-- More Email Requests (45 more) -->
        <record id="demo_email_3" model="bssic.request">
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="description">New email account request</field>
            <field name="state">completed</field>
            <field name="show_email_fields" eval="True"/>
        </record>

        <record id="demo_email_4" model="bssic.request">
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=57)).strftime('%Y-%m-%d')"/>
            <field name="description">Email configuration update</field>
            <field name="state">submitted</field>
            <field name="show_email_fields" eval="True"/>
        </record>

        <!-- More Permission Requests (45 more) -->
        <record id="demo_permission_3" model="bssic.request">
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="description">Permission request for system access</field>
            <field name="state">completed</field>
            <field name="permission_type">add</field>
            <field name="user_name">user200</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=180)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <record id="demo_permission_4" model="bssic.request">
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=59)).strftime('%Y-%m-%d')"/>
            <field name="description">Modify user permissions</field>
            <field name="state">direct_manager</field>
            <field name="permission_type">modify</field>
            <field name="user_name">user201</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=59)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <!-- More Authorization Delegation Requests (35 more) -->
        <record id="demo_auth_3" model="bssic.request">
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="description">Authorization delegation request</field>
            <field name="state">completed</field>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <record id="demo_auth_4" model="bssic.request">
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=61)).strftime('%Y-%m-%d')"/>
            <field name="description">Temporary delegation for vacation</field>
            <field name="state">audit_manager</field>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <!-- More Free Entry Requests (75 more) -->
        <record id="demo_free_4" model="bssic.request">
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=62)).strftime('%Y-%m-%d')"/>
            <field name="description">Free form request for office supplies</field>
            <field name="state">completed</field>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_free_5" model="bssic.request">
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=63)).strftime('%Y-%m-%d')"/>
            <field name="description">Training room booking</field>
            <field name="state">submitted</field>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_free_6" model="bssic.request">
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=64)).strftime('%Y-%m-%d')"/>
            <field name="description">Equipment maintenance request</field>
            <field name="state">direct_manager</field>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

    </data>
</odoo>
