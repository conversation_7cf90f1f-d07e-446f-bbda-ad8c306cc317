<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Final Demo Request Data - Additional requests to reach 500+ total -->
        
        <!-- More Password Reset Requests -->
        <record id="demo_request_password_200" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=102)).strftime('%Y-%m-%d')"/>
            <field name="description">Password reset for system access</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_5"/>
            <field name="username">user300</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_request_password_201" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=103)).strftime('%Y-%m-%d')"/>
            <field name="description">Account reactivation needed</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_6"/>
            <field name="username">user301</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_request_password_202" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=104)).strftime('%Y-%m-%d')"/>
            <field name="description">Swift system password reset</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_7"/>
            <field name="username">user302</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- More USB Access Requests -->
        <record id="demo_request_usb_200" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=105)).strftime('%Y-%m-%d')"/>
            <field name="description">Data backup request</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_8"/>
            <field name="usb_purpose">Data backup</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Documents</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_request_usb_201" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=106)).strftime('%Y-%m-%d')"/>
            <field name="description">File transfer for presentation</field>
            <field name="state">it_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_9"/>
            <field name="usb_purpose">Presentation materials</field>
            <field name="usb_duration">2 days</field>
            <field name="data_type">Presentations</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- More Technical Requests -->
        <record id="demo_request_technical_200" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=107)).strftime('%Y-%m-%d')"/>
            <field name="description">Computer not starting up</field>
            <field name="state">assigned</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_10"/>
            <field name="priority">2</field>
        </record>

        <record id="demo_request_technical_201" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=108)).strftime('%Y-%m-%d')"/>
            <field name="description">Software installation needed</field>
            <field name="state">in_progress</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_finance_staff_1"/>
            <field name="priority">1</field>
        </record>

        <record id="demo_request_technical_202" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=109)).strftime('%Y-%m-%d')"/>
            <field name="description">Printer not working</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_finance_staff_2"/>
            <field name="priority">1</field>
        </record>

        <!-- More Extension Requests -->
        <record id="demo_request_extension_200" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=110)).strftime('%Y-%m-%d')"/>
            <field name="description">Extension request for system access</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_hr_staff_1"/>
            <field name="extension_duration">3 months</field>
            <field name="extension_reason">Project continuation</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <!-- More Email Requests -->
        <record id="demo_request_email_200" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=111)).strftime('%Y-%m-%d')"/>
            <field name="description">New email account request</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_ops_staff_1"/>
            <field name="show_email_fields" eval="True"/>
        </record>

        <!-- More Permission Requests -->
        <record id="demo_request_permission_200" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=112)).strftime('%Y-%m-%d')"/>
            <field name="description">Permission request for system access</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_ops_staff_2"/>
            <field name="permission_type">add</field>
            <field name="user_name">user500</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=112)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=180)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <!-- More Authorization Delegation Requests -->
        <record id="demo_request_auth_200" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=113)).strftime('%Y-%m-%d')"/>
            <field name="description">Authorization delegation request</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_risk_staff_1"/>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <!-- More Free Entry Requests -->
        <record id="demo_request_free_200" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=114)).strftime('%Y-%m-%d')"/>
            <field name="description">Free form request for office supplies</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_audit_staff_1"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_201" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=115)).strftime('%Y-%m-%d')"/>
            <field name="description">Training room booking</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_banking_staff_1"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_202" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=116)).strftime('%Y-%m-%d')"/>
            <field name="description">Equipment maintenance request</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_banking_staff_2"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_203" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=117)).strftime('%Y-%m-%d')"/>
            <field name="description">Meeting room reservation</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_it_manager"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_204" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=118)).strftime('%Y-%m-%d')"/>
            <field name="description">Parking space request</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_finance_manager"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_205" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=119)).strftime('%Y-%m-%d')"/>
            <field name="description">Building access card</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_hr_manager"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_206" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=120)).strftime('%Y-%m-%d')"/>
            <field name="description">Visitor access arrangement</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_ops_manager"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

    </data>
</odoo>
