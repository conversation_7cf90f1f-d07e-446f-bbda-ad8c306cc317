#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to generate demo request data for BSSIC Requests module
This script generates XML records for demo purposes
"""

import random
from datetime import datetime, timedelta

# Request types and their configurations
REQUEST_TYPES = {
    'password_reset': {
        'ref': 'bssic_requests.request_type_password_reset',
        'fields': ['username', 'device_type', 'request_reason', 'show_password_fields'],
        'device_types': ['internet', 'system', 'swift', 'other'],
        'request_reasons': ['password_reset', 'account_reactivation'],
        'descriptions': [
            'Password reset request for system access',
            'Account locked - reactivation needed',
            'Forgotten password for internet access',
            'Swift system password reset',
            'System login issues'
        ]
    },
    'usb': {
        'ref': 'bssic_requests.request_type_usb',
        'fields': ['usb_purpose', 'usb_duration', 'data_type', 'show_usb_fields'],
        'usb_purposes': ['Data backup', 'File transfer', 'Document sharing', 'Presentation materials', 'Report distribution'],
        'usb_durations': ['1 day', '2 days', '3 days', '1 week', '2 weeks'],
        'data_types': ['Documents', 'Presentations', 'Reports', 'Financial data', 'Customer data'],
        'descriptions': [
            'USB access request for data transfer',
            'File transfer for presentation',
            'Financial reports transfer',
            'Customer data backup',
            'Document sharing with external party'
        ]
    },
    'extension': {
        'ref': 'bssic_requests.request_type_extension',
        'fields': ['extension_duration', 'extension_reason', 'show_extension_fields'],
        'extension_durations': ['1 month', '3 months', '6 months', '1 year'],
        'extension_reasons': ['Project continuation', 'Ongoing work requirements', 'Extended responsibilities', 'Temporary assignment'],
        'descriptions': [
            'Extension request for system access',
            'Extended access for ongoing work',
            'Temporary assignment extension',
            'Project continuation access',
            'Extended responsibilities access'
        ]
    },
    'technical': {
        'ref': 'bssic_requests.request_type_technical',
        'fields': ['priority'],
        'priorities': ['0', '1', '2', '3'],
        'descriptions': [
            'Computer not starting up',
            'Software installation needed',
            'Printer not working',
            'Network connectivity issues',
            'System access problem',
            'Hardware replacement required',
            'Software update needed',
            'Data recovery request',
            'Email configuration issue',
            'VPN access problem',
            'Monitor display issues',
            'Keyboard/mouse malfunction',
            'Application crashes frequently',
            'Slow system performance',
            'File sharing problems'
        ]
    },
    'email': {
        'ref': 'bssic_requests.request_type_email',
        'fields': ['show_email_fields'],
        'descriptions': [
            'New email account request',
            'Email configuration update',
            'Email forwarding setup',
            'Distribution list access',
            'Email signature setup'
        ]
    },
    'permission': {
        'ref': 'bssic_requests.request_type_permission',
        'fields': ['permission_type', 'user_name', 'validity_from', 'validity_to', 'show_permission_fields'],
        'permission_types': ['add', 'modify', 'delete', 'withdraw', 'activate', 'deactivate'],
        'descriptions': [
            'Permission request for system access',
            'Modify user permissions',
            'Delete user permissions',
            'Activate user account',
            'Deactivate user access',
            'Withdraw user permissions'
        ]
    },
    'authorization_delegation': {
        'ref': 'bssic_requests.request_type_authorization_delegation',
        'fields': ['show_authorization_delegation_fields'],
        'descriptions': [
            'Authorization delegation request',
            'Temporary delegation for vacation',
            'Emergency authorization delegation',
            'Project-based delegation'
        ]
    },
    'free_entry': {
        'ref': 'bssic_requests.request_type_free_entry',
        'fields': ['show_free_entry_fields'],
        'descriptions': [
            'Free form request for office supplies',
            'Training room booking',
            'Equipment maintenance request',
            'Meeting room reservation',
            'Parking space request',
            'Building access card',
            'Visitor access arrangement'
        ]
    }
}

# Employee references
EMPLOYEES = [
    'bssic_requests.hr_employee_staff_1',
    'bssic_requests.hr_employee_staff_2',
    'bssic_requests.hr_employee_staff_3',
    'bssic_requests.hr_employee_staff_4',
    'bssic_requests.hr_employee_staff_5',
    'bssic_requests.hr_employee_staff_6',
    'bssic_requests.hr_employee_staff_7',
    'bssic_requests.hr_employee_staff_8',
    'bssic_requests.hr_employee_staff_9',
    'bssic_requests.hr_employee_staff_10',
    'bssic_requests.hr_employee_finance_staff_1',
    'bssic_requests.hr_employee_finance_staff_2',
    'bssic_requests.hr_employee_hr_staff_1',
    'bssic_requests.hr_employee_ops_staff_1',
    'bssic_requests.hr_employee_ops_staff_2',
    'bssic_requests.hr_employee_risk_staff_1',
    'bssic_requests.hr_employee_audit_staff_1',
    'bssic_requests.hr_employee_banking_staff_1',
    'bssic_requests.hr_employee_banking_staff_2',
    'bssic_requests.hr_employee_it_staff_1',
    'bssic_requests.hr_employee_it_staff_2'
]

# Request states
STATES = ['draft', 'submitted', 'direct_manager', 'audit_manager', 'it_manager', 'assigned', 'in_progress', 'completed', 'rejected']

def generate_date(days_ago_min=1, days_ago_max=90):
    """Generate a random date within the specified range"""
    days_ago = random.randint(days_ago_min, days_ago_max)
    date = datetime.now() - timedelta(days=days_ago)
    return date.strftime('%Y-%m-%d')

def generate_request_xml(request_id, request_type, index):
    """Generate XML for a single request"""
    config = REQUEST_TYPES[request_type]
    
    xml = f'        <record id="demo_request_{request_type}_{index}" model="bssic.request">\n'
    xml += f'            <field name="request_type_id" ref="{config["ref"]}"/>\n'
    xml += f'            <field name="request_type_code">{request_type}</field>\n'
    xml += f'            <field name="request_date" eval="(DateTime.now() - timedelta(days={random.randint(1, 90)})).strftime(\'%Y-%m-%d\')"/>\n'
    xml += f'            <field name="description">{random.choice(config["descriptions"])}</field>\n'
    xml += f'            <field name="state">{random.choice(STATES)}</field>\n'
    xml += f'            <field name="employee_id" ref="{random.choice(EMPLOYEES)}"/>\n'
    
    # Add specific fields based on request type
    if request_type == 'password_reset':
        xml += f'            <field name="username">user{random.randint(100, 999)}</field>\n'
        xml += f'            <field name="device_type">{random.choice(config["device_types"])}</field>\n'
        xml += f'            <field name="request_reason">{random.choice(config["request_reasons"])}</field>\n'
        xml += f'            <field name="show_password_fields" eval="True"/>\n'
    
    elif request_type == 'usb':
        xml += f'            <field name="usb_purpose">{random.choice(config["usb_purposes"])}</field>\n'
        xml += f'            <field name="usb_duration">{random.choice(config["usb_durations"])}</field>\n'
        xml += f'            <field name="data_type">{random.choice(config["data_types"])}</field>\n'
        xml += f'            <field name="show_usb_fields" eval="True"/>\n'
    
    elif request_type == 'extension':
        xml += f'            <field name="extension_duration">{random.choice(config["extension_durations"])}</field>\n'
        xml += f'            <field name="extension_reason">{random.choice(config["extension_reasons"])}</field>\n'
        xml += f'            <field name="show_extension_fields" eval="True"/>\n'
    
    elif request_type == 'technical':
        xml += f'            <field name="priority">{random.choice(config["priorities"])}</field>\n'
    
    elif request_type == 'email':
        xml += f'            <field name="show_email_fields" eval="True"/>\n'
    
    elif request_type == 'permission':
        xml += f'            <field name="permission_type">{random.choice(config["permission_types"])}</field>\n'
        xml += f'            <field name="user_name">user{random.randint(100, 999)}</field>\n'
        xml += f'            <field name="validity_from" eval="(DateTime.now() - timedelta(days={random.randint(1, 30)})).strftime(\'%Y-%m-%d\')"/>\n'
        xml += f'            <field name="validity_to" eval="(DateTime.now() + timedelta(days={random.randint(30, 365)})).strftime(\'%Y-%m-%d\')"/>\n'
        xml += f'            <field name="show_permission_fields" eval="True"/>\n'
    
    elif request_type == 'authorization_delegation':
        xml += f'            <field name="show_authorization_delegation_fields" eval="True"/>\n'
    
    elif request_type == 'free_entry':
        xml += f'            <field name="show_free_entry_fields" eval="True"/>\n'
    
    xml += f'        </record>\n\n'
    return xml

def generate_bulk_requests_xml():
    """Generate XML for bulk requests"""
    xml_content = '''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Bulk Demo Request Data - 400+ Additional Requests -->
        
'''
    
    # Generate requests for each type
    request_counts = {
        'password_reset': 60,
        'usb': 60,
        'extension': 40,
        'technical': 120,
        'email': 40,
        'permission': 40,
        'authorization_delegation': 30,
        'free_entry': 80
    }
    
    for request_type, count in request_counts.items():
        xml_content += f'        <!-- {request_type.replace("_", " ").title()} Requests -->\n'
        for i in range(count):
            xml_content += generate_request_xml(f'{request_type}_{i}', request_type, i + 10)
    
    xml_content += '''    </data>
</odoo>'''
    
    return xml_content

if __name__ == "__main__":
    print("Generating bulk demo requests XML...")
    xml_content = generate_bulk_requests_xml()
    
    with open('request_demo_data_bulk.xml', 'w', encoding='utf-8') as f:
        f.write(xml_content)
    
    print("Generated request_demo_data_bulk.xml with 470+ demo requests!")
