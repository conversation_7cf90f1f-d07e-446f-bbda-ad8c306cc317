<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Extended Demo Request Data - Additional 400+ Requests -->
        
        <!-- Password Reset Requests (50 more) -->
        <record id="demo_request_password_4" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="description">Password reset for internet access</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_risk_staff_1"/>
            <field name="username">user004</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_request_password_5" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="description">Account locked - reactivation needed</field>
            <field name="state">rejected</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_audit_staff_1"/>
            <field name="username">user005</field>
            <field name="device_type">system</field>
            <field name="request_reason">account_reactivation</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_request_password_6" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=35)).strftime('%Y-%m-%d')"/>
            <field name="description">Swift system access issue</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_banking_staff_1"/>
            <field name="username">user006</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- USB Access Requests (50 more) -->
        <record id="demo_request_usb_3" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="description">Financial reports transfer</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_banking_staff_2"/>
            <field name="usb_purpose">Financial data transfer</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Financial reports</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_request_usb_4" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="description">Customer data backup</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_5"/>
            <field name="usb_purpose">Data backup</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Customer data</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- Technical Requests (100 more) -->
        <record id="demo_request_technical_7" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="description">Email configuration issue</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_6"/>
            <field name="priority">2</field>
        </record>

        <record id="demo_request_technical_8" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="description">VPN access problem</field>
            <field name="state">in_progress</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_7"/>
            <field name="priority">3</field>
        </record>

        <record id="demo_request_technical_9" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="description">Data recovery request</field>
            <field name="state">assigned</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_8"/>
            <field name="priority">3</field>
        </record>

        <record id="demo_request_technical_10" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="description">System access problem</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_9"/>
            <field name="priority">1</field>
        </record>

        <!-- Extension Requests (50 more) -->
        <record id="demo_request_extension_3" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="description">Temporary assignment extension</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_10"/>
            <field name="extension_duration">1 year</field>
            <field name="extension_reason">Temporary assignment</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <record id="demo_request_extension_4" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=33)).strftime('%Y-%m-%d')"/>
            <field name="description">Extended responsibilities access</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_finance_manager"/>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Extended responsibilities</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <!-- Permission Requests (50 more) -->
        <record id="demo_request_permission_3" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="description">Delete user permissions</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_hr_manager"/>
            <field name="permission_type">delete</field>
            <field name="user_name">user789</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=90)).strftime('%Y-%m-%d')"/>
            <field name="operations_dept" eval="True"/>
            <field name="operations_level">user</field>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <record id="demo_request_permission_4" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=40)).strftime('%Y-%m-%d')"/>
            <field name="description">Activate user account</field>
            <field name="state">rejected</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_ops_manager"/>
            <field name="permission_type">activate</field>
            <field name="user_name">user101</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=40)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=270)).strftime('%Y-%m-%d')"/>
            <field name="banking_operations" eval="True"/>
            <field name="banking_level">user</field>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <!-- Email Requests (50 more) -->
        <record id="demo_request_email_3" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="description">Email forwarding setup</field>
            <field name="state">it_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_risk_manager"/>
            <field name="show_email_fields" eval="True"/>
        </record>

        <record id="demo_request_email_4" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=21)).strftime('%Y-%m-%d')"/>
            <field name="description">Distribution list access</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_audit_manager"/>
            <field name="show_email_fields" eval="True"/>
        </record>

        <!-- Authorization Delegation Requests (50 more) -->
        <record id="demo_request_auth_2" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="description">Temporary delegation for vacation</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_banking_manager"/>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <!-- Free Entry Requests (100 more) -->
        <record id="demo_request_free_2" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="description">Training room booking</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_it_staff_1"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_3" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="description">Equipment maintenance request</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_it_staff_2"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

    </data>
</odoo>
