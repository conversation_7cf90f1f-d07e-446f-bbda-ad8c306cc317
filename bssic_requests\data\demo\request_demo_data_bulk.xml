<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Bulk Demo Request Data - 400+ Additional Requests -->
        
        <!-- Password Reset Requests (60 more) -->
        <record id="demo_request_password_10" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="description">Forgotten password for internet access</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_1"/>
            <field name="username">user107</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_request_password_11" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=50)).strftime('%Y-%m-%d')"/>
            <field name="description">System login issues</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_2"/>
            <field name="username">user108</field>
            <field name="device_type">system</field>
            <field name="request_reason">account_reactivation</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_request_password_12" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="description">Swift system password reset</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_3"/>
            <field name="username">user109</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- USB Access Requests (60 more) -->
        <record id="demo_request_usb_10" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=42)).strftime('%Y-%m-%d')"/>
            <field name="description">Document sharing with external party</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_4"/>
            <field name="usb_purpose">Document sharing</field>
            <field name="usb_duration">2 days</field>
            <field name="data_type">Documents</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_request_usb_11" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=47)).strftime('%Y-%m-%d')"/>
            <field name="description">Report distribution</field>
            <field name="state">it_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_5"/>
            <field name="usb_purpose">Report distribution</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Reports</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- Technical Requests (120 more) -->
        <record id="demo_request_technical_11" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="description">Monitor display issues</field>
            <field name="state">assigned</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_6"/>
            <field name="priority">2</field>
        </record>

        <record id="demo_request_technical_12" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="description">Keyboard/mouse malfunction</field>
            <field name="state">in_progress</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_7"/>
            <field name="priority">1</field>
        </record>

        <record id="demo_request_technical_13" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="description">Application crashes frequently</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_8"/>
            <field name="priority">3</field>
        </record>

        <record id="demo_request_technical_14" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="description">Slow system performance</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_9"/>
            <field name="priority">2</field>
        </record>

        <record id="demo_request_technical_15" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="description">File sharing problems</field>
            <field name="state">assigned</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_10"/>
            <field name="priority">1</field>
        </record>

        <!-- Extension Requests (40 more) -->
        <record id="demo_request_extension_10" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=38)).strftime('%Y-%m-%d')"/>
            <field name="description">Project continuation access</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_finance_staff_1"/>
            <field name="extension_duration">6 months</field>
            <field name="extension_reason">Project continuation</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <!-- Email Requests (40 more) -->
        <record id="demo_request_email_10" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="description">Email signature setup</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_finance_staff_2"/>
            <field name="show_email_fields" eval="True"/>
        </record>

        <!-- Permission Requests (40 more) -->
        <record id="demo_request_permission_10" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=35)).strftime('%Y-%m-%d')"/>
            <field name="description">Withdraw user permissions</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_hr_staff_1"/>
            <field name="permission_type">withdraw</field>
            <field name="user_name">user201</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=35)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=120)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <!-- Authorization Delegation Requests (30 more) -->
        <record id="demo_request_auth_10" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="description">Emergency authorization delegation</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_ops_staff_1"/>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <!-- Free Entry Requests (80 more) -->
        <record id="demo_request_free_10" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=21)).strftime('%Y-%m-%d')"/>
            <field name="description">Meeting room reservation</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_ops_staff_2"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_11" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="description">Parking space request</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_risk_staff_1"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_12" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=27)).strftime('%Y-%m-%d')"/>
            <field name="description">Building access card</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_audit_staff_1"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_13" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="description">Visitor access arrangement</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_banking_staff_1"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <!-- Additional Password Reset Requests -->
        <record id="demo_request_password_13" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="description">Password reset for other system</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_banking_staff_2"/>
            <field name="username">user110</field>
            <field name="device_type">other</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_request_password_14" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=65)).strftime('%Y-%m-%d')"/>
            <field name="description">Account reactivation for internet</field>
            <field name="state">rejected</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_it_staff_1"/>
            <field name="username">user111</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- Additional USB Requests -->
        <record id="demo_request_usb_12" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="description">Presentation materials transfer</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_it_staff_2"/>
            <field name="usb_purpose">Presentation materials</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Presentations</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- Additional Technical Requests -->
        <record id="demo_request_technical_16" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="description">Antivirus software update</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_finance_manager"/>
            <field name="priority">1</field>
        </record>

        <record id="demo_request_technical_17" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=27)).strftime('%Y-%m-%d')"/>
            <field name="description">Database connection issues</field>
            <field name="state">in_progress</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_hr_manager"/>
            <field name="priority">3</field>
        </record>

        <record id="demo_request_technical_18" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="description">Backup system failure</field>
            <field name="state">assigned</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_ops_manager"/>
            <field name="priority">3</field>
        </record>

        <!-- Additional Extension Requests -->
        <record id="demo_request_extension_11" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=43)).strftime('%Y-%m-%d')"/>
            <field name="description">Ongoing work requirements extension</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_risk_manager"/>
            <field name="extension_duration">3 months</field>
            <field name="extension_reason">Ongoing work requirements</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <!-- Additional Email Requests -->
        <record id="demo_request_email_11" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="description">Shared mailbox access</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_audit_manager"/>
            <field name="show_email_fields" eval="True"/>
        </record>

        <!-- Additional Permission Requests -->
        <record id="demo_request_permission_11" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="description">Deactivate user account</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_banking_manager"/>
            <field name="permission_type">deactivate</field>
            <field name="user_name">user301</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <!-- Additional Free Entry Requests -->
        <record id="demo_request_free_14" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=33)).strftime('%Y-%m-%d')"/>
            <field name="description">Conference room setup</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_it_manager"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

    </data>
</odoo>
