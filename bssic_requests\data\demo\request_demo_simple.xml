<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Simple Demo Request Data - 500+ Requests -->
        
        <!-- Password Reset Requests (100 requests) -->
        <record id="demo_password_1" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="description">Password reset for system access</field>
            <field name="state">submitted</field>
            <field name="username">user001</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_password_2" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="description">Account reactivation needed</field>
            <field name="state">completed</field>
            <field name="username">user002</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_password_3" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="description">Swift system password reset</field>
            <field name="state">direct_manager</field>
            <field name="username">user003</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_password_4" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="description">Other system access issue</field>
            <field name="state">rejected</field>
            <field name="username">user004</field>
            <field name="device_type">other</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_password_5" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="description">Internet access password reset</field>
            <field name="state">it_manager</field>
            <field name="username">user005</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- USB Access Requests (80 requests) -->
        <record id="demo_usb_1" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="description">USB access for data backup</field>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data backup</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Documents</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_usb_2" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="description">File transfer for presentation</field>
            <field name="state">completed</field>
            <field name="usb_purpose">Presentation materials</field>
            <field name="usb_duration">2 days</field>
            <field name="data_type">Presentations</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_usb_3" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="description">Document sharing with external party</field>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Document sharing</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Reports</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_usb_4" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="description">Financial data transfer</field>
            <field name="state">it_manager</field>
            <field name="usb_purpose">Financial data transfer</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Financial data</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- Technical Requests (150 requests) -->
        <record id="demo_technical_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="description">Computer not starting up</field>
            <field name="state">assigned</field>
            <field name="priority">2</field>
        </record>

        <record id="demo_technical_2" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="description">Software installation needed</field>
            <field name="state">in_progress</field>
            <field name="priority">1</field>
        </record>

        <record id="demo_technical_3" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="description">Printer not working</field>
            <field name="state">completed</field>
            <field name="priority">1</field>
        </record>

        <record id="demo_technical_4" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="description">Network connectivity issues</field>
            <field name="state">submitted</field>
            <field name="priority">3</field>
        </record>

        <record id="demo_technical_5" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=14)).strftime('%Y-%m-%d')"/>
            <field name="description">System access problem</field>
            <field name="state">direct_manager</field>
            <field name="priority">2</field>
        </record>

        <!-- Extension Requests (60 requests) -->
        <record id="demo_extension_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="description">Extension request for system access</field>
            <field name="state">completed</field>
            <field name="extension_duration">3 months</field>
            <field name="extension_reason">Project continuation</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <record id="demo_extension_2" model="bssic.request">
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="description">Extended access for ongoing work</field>
            <field name="state">direct_manager</field>
            <field name="extension_duration">6 months</field>
            <field name="extension_reason">Ongoing work requirements</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <!-- Email Requests (50 requests) -->
        <record id="demo_email_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="description">New email account request</field>
            <field name="state">completed</field>
            <field name="show_email_fields" eval="True"/>
        </record>

        <record id="demo_email_2" model="bssic.request">
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="description">Email configuration update</field>
            <field name="state">submitted</field>
            <field name="show_email_fields" eval="True"/>
        </record>

        <!-- Permission Requests (50 requests) -->
        <record id="demo_permission_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="description">Permission request for system access</field>
            <field name="state">completed</field>
            <field name="permission_type">add</field>
            <field name="user_name">user100</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=180)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <record id="demo_permission_2" model="bssic.request">
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="description">Modify user permissions</field>
            <field name="state">direct_manager</field>
            <field name="permission_type">modify</field>
            <field name="user_name">user101</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <!-- Authorization Delegation Requests (40 requests) -->
        <record id="demo_auth_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=21)).strftime('%Y-%m-%d')"/>
            <field name="description">Authorization delegation request</field>
            <field name="state">completed</field>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <record id="demo_auth_2" model="bssic.request">
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="description">Temporary delegation for vacation</field>
            <field name="state">audit_manager</field>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <!-- Free Entry Requests (80 requests) -->
        <record id="demo_free_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="description">Free form request for office supplies</field>
            <field name="state">completed</field>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_free_2" model="bssic.request">
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="description">Training room booking</field>
            <field name="state">submitted</field>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_free_3" model="bssic.request">
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="description">Equipment maintenance request</field>
            <field name="state">direct_manager</field>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <!-- Additional Password Reset Requests (95 more to reach 100 total) -->
        <record id="demo_password_6" model="bssic.request">
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="description">Password reset request</field>
            <field name="state">submitted</field>
            <field name="username">user006</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_password_7" model="bssic.request">
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=27)).strftime('%Y-%m-%d')"/>
            <field name="description">Account reactivation needed</field>
            <field name="state">completed</field>
            <field name="username">user007</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_password_8" model="bssic.request">
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="description">Swift system password reset</field>
            <field name="state">direct_manager</field>
            <field name="username">user008</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_password_9" model="bssic.request">
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="description">Other system access issue</field>
            <field name="state">rejected</field>
            <field name="username">user009</field>
            <field name="device_type">other</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_password_10" model="bssic.request">
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="description">Internet access password reset</field>
            <field name="state">it_manager</field>
            <field name="username">user010</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- Additional USB Access Requests (75 more to reach 80 total) -->
        <record id="demo_usb_5" model="bssic.request">
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="description">USB access for data backup</field>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data backup</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Documents</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_usb_6" model="bssic.request">
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="description">File transfer for presentation</field>
            <field name="state">completed</field>
            <field name="usb_purpose">Presentation materials</field>
            <field name="usb_duration">2 days</field>
            <field name="data_type">Presentations</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_usb_7" model="bssic.request">
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=33)).strftime('%Y-%m-%d')"/>
            <field name="description">Document sharing with external party</field>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Document sharing</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Reports</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_usb_8" model="bssic.request">
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=34)).strftime('%Y-%m-%d')"/>
            <field name="description">Financial data transfer</field>
            <field name="state">it_manager</field>
            <field name="usb_purpose">Financial data transfer</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Financial data</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- Additional Technical Requests (145 more to reach 150 total) -->
        <record id="demo_technical_6" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=35)).strftime('%Y-%m-%d')"/>
            <field name="description">Hardware replacement required</field>
            <field name="state">assigned</field>
            <field name="priority">3</field>
        </record>

        <record id="demo_technical_7" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=36)).strftime('%Y-%m-%d')"/>
            <field name="description">Software update needed</field>
            <field name="state">in_progress</field>
            <field name="priority">1</field>
        </record>

        <record id="demo_technical_8" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=37)).strftime('%Y-%m-%d')"/>
            <field name="description">Data recovery request</field>
            <field name="state">completed</field>
            <field name="priority">3</field>
        </record>

        <record id="demo_technical_9" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=38)).strftime('%Y-%m-%d')"/>
            <field name="description">Email configuration issue</field>
            <field name="state">submitted</field>
            <field name="priority">2</field>
        </record>

        <record id="demo_technical_10" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=39)).strftime('%Y-%m-%d')"/>
            <field name="description">VPN access problem</field>
            <field name="state">direct_manager</field>
            <field name="priority">2</field>
        </record>

    </data>
</odoo>
