<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Minimal Demo Request Data - Just a few requests to test -->
        
        <!-- Password Reset Request -->
        <record id="demo_password_minimal_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="description">Password reset for system access</field>
            <field name="state">submitted</field>
            <field name="username">user001</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- USB Access Request -->
        <record id="demo_usb_minimal_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="description">USB access for data backup</field>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data backup</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Documents</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- Technical Request -->
        <record id="demo_technical_minimal_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="description">Computer not starting up</field>
            <field name="state">assigned</field>
            <field name="priority">2</field>
        </record>

        <!-- Extension Request -->
        <record id="demo_extension_minimal_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="description">Extension request for system access</field>
            <field name="state">completed</field>
            <field name="extension_duration">3 months</field>
            <field name="extension_reason">Project continuation</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <!-- Email Request -->
        <record id="demo_email_minimal_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="description">New email account request</field>
            <field name="state">completed</field>
            <field name="show_email_fields" eval="True"/>
        </record>

        <!-- Permission Request -->
        <record id="demo_permission_minimal_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="description">Permission request for system access</field>
            <field name="state">completed</field>
            <field name="permission_type">add</field>
            <field name="user_name">user100</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=180)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <!-- Authorization Delegation Request -->
        <record id="demo_auth_minimal_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="description">Authorization delegation request</field>
            <field name="state">completed</field>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <!-- Free Entry Request -->
        <record id="demo_free_minimal_1" model="bssic.request">
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="description">Free form request for office supplies</field>
            <field name="state">completed</field>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

    </data>
</odoo>
