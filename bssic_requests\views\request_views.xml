<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Request Form View -->
    <record id="view_bssic_request_form" model="ir.ui.view">
        <field name="name">bssic.request.form</field>
        <field name="model">bssic.request</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_submit" string="Submit" type="object" class="oe_highlight" states="draft"/>
                    <button name="action_approve_direct_manager" string="Approve (Direct Manager)" type="object" class="oe_highlight"
                            states="direct_manager" groups="bssic_requests.group_bssic_direct_manager"/>
                    <button name="action_approve_audit_manager" string="Approve (Audit Manager)" type="object" class="oe_highlight"
                            attrs="{'invisible': [('is_technical', '=', True)]}"
                            states="audit_manager" groups="bssic_requests.group_bssic_audit_manager"/>
                    <button name="action_approve_it_manager" string="Approve (IT Manager)" type="object" class="oe_highlight"
                            states="it_manager" groups="bssic_requests.group_bssic_it_manager"/>
                    <button name="action_assign" string="Assign to IT Staff" type="object" class="oe_highlight"
                            states="assigned" groups="bssic_requests.group_bssic_it_manager"/>
                    <button name="action_complete" string="Mark as Completed" type="object" class="oe_highlight"
                            states="in_progress" groups="bssic_requests.group_bssic_it_staff"/>
                    <button name="action_reject" string="Reject" type="object" class="btn-danger"
                            states="direct_manager,audit_manager,it_manager,assigned,in_progress"/>
                    <field name="is_technical" invisible="1"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submitted,direct_manager,audit_manager,it_manager,assigned,in_progress,completed"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                        <h3>
                            <field name="request_type_id" readonly="1" options="{'no_open': True}"/>
                        </h3>
                    </div>
                    <group>
                        <group>
                            <field name="is_manager" invisible="1"/>
                            <field name="employee_number" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="employee_id" options="{'no_create': True}"
                                   attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="department_id" readonly="1"/>
                            <field name="job_id" readonly="1"/>
                            <field name="request_type_code" invisible="1"/>
                            <field name="show_password_fields" invisible="1"/>
                            <field name="show_usb_fields" invisible="1"/>
                            <field name="show_extension_fields" invisible="1"/>
                            <field name="show_permission_fields" invisible="1"/>
                            <field name="show_email_fields" invisible="1"/>
                            <field name="show_authorization_delegation_fields" invisible="1"/>
                            <field name="show_free_entry_fields" invisible="1"/>
                            <field name="show_technical_fields" invisible="1"/>
                        </group>
                        <group>
                            <field name="request_date" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="assigned_to" options="{'no_create': True, 'no_quick_create': True}"
                                   attrs="{'invisible': [('state', 'not in', ['assigned', 'in_progress', 'completed', 'rejected'])],
                                          'readonly': [('state', 'not in', ['assigned'])],
                                          'required': [('state', '=', 'assigned')]}"
                                   context="{'it_staff_only': True}"
                                   groups="bssic_requests.group_bssic_it_manager"
                                   help="Select an IT staff member to assign this request to. Only employees with IT Staff permissions are shown."/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Request Details" name="request_details">
                            <group>
                                <group>
                                    <field name="request_type_id" options="{'no_create': True}" invisible="1"/>
                                    <field name="request_nature" widget="radio" attrs="{'invisible': [('show_technical_fields', '=', False)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="technical_category_id" attrs="{'invisible': [('show_technical_fields', '=', False)], 'required': [('show_technical_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                                <group>
                                    <field name="technical_subcategory_id" attrs="{'invisible': [('show_technical_fields', '=', False)], 'required': [('show_technical_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="priority" widget="priority" attrs="{'invisible': [('show_technical_fields', '=', False)], 'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                            </group>
                            <group>
                                <field name="description" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            </group>

                            <!-- Password Reset Request Fields -->
                            <group attrs="{'invisible': [('show_password_fields', '=', False)]}">
                                <group string="Password Reset Details">
                                    <field name="username" attrs="{'required': [('show_password_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="device_type" attrs="{'required': [('show_password_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="request_reason" attrs="{'required': [('show_password_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                            </group>

                            <!-- USB Request Fields -->
                            <group attrs="{'invisible': [('show_usb_fields', '=', False)]}">
                                <group string="USB Usage Details">
                                    <field name="usb_purpose" attrs="{'required': [('show_usb_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="usb_duration" attrs="{'required': [('show_usb_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="data_type" attrs="{'required': [('show_usb_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                            </group>

                            <!-- Extension Request Fields -->
                            <group attrs="{'invisible': [('show_extension_fields', '=', False)]}">
                                <group string="Extension Details">
                                    <field name="extension_duration" attrs="{'required': [('show_extension_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="extension_reason" attrs="{'required': [('show_extension_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                            </group>

                            <!-- Permission Request Fields -->
                            <group attrs="{'invisible': [('show_permission_fields', '=', False)]}">
                                <group string="Permission Request Details">
                                    <field name="permission_type" attrs="{'required': [('show_permission_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="user_name" attrs="{'required': [('show_permission_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="validity_from" attrs="{'required': [('show_permission_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="validity_to" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                                <notebook attrs="{'invisible': [('show_permission_fields', '=', False)]}">
                                    <page string="Department Permissions">
                                        <div class="alert alert-info" role="alert" style="margin-bottom: 10px;">
                                            <strong>ملاحظة:</strong> يجب اختيار قسم واحد على الأقل من الأقسام التالية قبل إرسال الطلب.
                                        </div>
                                        <group>
                                            <group string="Accounting Department">
                                                <field name="accounting_dept" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                                <field name="accounting_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('accounting_dept', '=', False)], 'required': [('accounting_dept', '=', True)]}"/>
                                            </group>
                                            <group string="Internal Auditing">
                                                <field name="internal_audit" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                                <field name="internal_audit_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('internal_audit', '=', False)], 'required': [('internal_audit', '=', True)]}"/>
                                            </group>
                                        </group>

                                        <group>
                                            <group string="Risk">
                                                <field name="risk_dept" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                                <field name="risk_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('risk_dept', '=', False)], 'required': [('risk_dept', '=', True)]}"/>
                                            </group>
                                            <group string="Back Office - Credits">
                                                <field name="back_office_credits" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                                <field name="back_office_credits_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('back_office_credits', '=', False)], 'required': [('back_office_credits', '=', True)]}"/>
                                            </group>
                                        </group>

                                        <group>
                                            <group string="Back Office - Deposits">
                                                <field name="back_office_deposits" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                                <field name="back_office_deposits_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('back_office_deposits', '=', False)], 'required': [('back_office_deposits', '=', True)]}"/>
                                            </group>
                                            <group string="Operations Department">
                                                <field name="operations_dept" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                                <field name="operations_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('operations_dept', '=', False)], 'required': [('operations_dept', '=', True)]}"/>
                                            </group>
                                        </group>

                                        <group>
                                            <group string="Forex Exchange">
                                                <field name="forex_exchange" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                                <field name="forex_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('forex_exchange', '=', False)], 'required': [('forex_exchange', '=', True)]}"/>
                                            </group>
                                            <group string="Banking Operations">
                                                <field name="banking_operations" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                                <field name="banking_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('banking_operations', '=', False)], 'required': [('banking_operations', '=', True)]}"/>
                                            </group>
                                        </group>

                                        <group>
                                            <group string="Personnel &amp; Admin">
                                                <field name="personnel_admin" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                                <field name="personnel_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('personnel_admin', '=', False)], 'required': [('personnel_admin', '=', True)]}"/>
                                            </group>
                                            <group string="Swift">
                                                <field name="swift" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                                <field name="swift_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('swift', '=', False)], 'required': [('swift', '=', True)]}"/>
                                            </group>
                                        </group>
                                    </page>

                                    <page string="Transaction Limits">
                                        <group>
                                            <group>
                                                <field name="transaction_amount_limit" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                            </group>
                                            <group>
                                                <field name="auth_limit" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                            </group>
                                            <group>
                                                <field name="max_amount_limit" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                            </group>
                                        </group>
                                    </page>
                                </notebook>
                            </group>

                            <!-- Email Request Fields -->
                            <group attrs="{'invisible': [('show_email_fields', '=', False)]}">
                                <group string="Email Request Details">
                                    <field name="email_type" widget="radio" attrs="{'required': [('show_email_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="email_reason" attrs="{'readonly': [('state', '!=', 'draft')], 'required': [('show_email_fields', '=', True)]}"/>
                                </group>

                                <!-- Email Agreement Section - Only visible when New Email is selected -->
                                <group attrs="{'invisible': [('email_type', '!=', 'new')]}">
                                    <field name="email_agreement_text" nolabel="1" attrs="{'readonly': True}"/>
                                    <field name="email_agreement_accepted" attrs="{'required': [('email_type', '=', 'new')], 'readonly': [('state', '!=', 'draft')]}" string="أوافق على الشروط والأحكام المذكورة أعلاه"/>
                                </group>
                            </group>

                            <!-- Authorization Delegation Request Fields -->
                            <group attrs="{'invisible': [('show_authorization_delegation_fields', '=', False)]}">
                                <group string="Authorization Delegation Details">
                                    <field name="ceiling_reason" string="Reason for Ceiling Increase" attrs="{'required': [('show_authorization_delegation_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="delegation_details" string="Details" attrs="{'required': [('show_authorization_delegation_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="delegation_from_date" string="From Date" attrs="{'required': [('show_authorization_delegation_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="delegation_to_date" string="To Date" attrs="{'required': [('show_authorization_delegation_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                                <group>
                                    <field name="delegation_max_amount" string="Max. Amount" attrs="{'required': [('show_authorization_delegation_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="delegation_auth_limit" string="Auth O.D. Limit" attrs="{'required': [('show_authorization_delegation_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                            </group>

                            <!-- Free Entry Request Fields -->
                            <group attrs="{'invisible': [('show_free_entry_fields', '=', False)]}">
                                <group string="Free Form Details">
                                    <field name="free_entry_subject" string="Subject" attrs="{'required': [('show_free_entry_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="free_entry_user_name" string="User Name" attrs="{'required': [('show_free_entry_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="free_entry_type" string="Entry Type" attrs="{'required': [('show_free_entry_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="free_entry_from_date" string="From Date" attrs="{'required': [('show_free_entry_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="free_entry_to_date" string="To Date" attrs="{'required': [('show_free_entry_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                                <group>
                                    <field name="free_entry_details" string="Operation Details" attrs="{'required': [('show_free_entry_fields', '=', True)], 'readonly': [('state', '!=', 'draft')]}" widget="text"/>
                                </group>
                            </group>

                            <!-- Technical Request Fields -->
                            <group attrs="{'invisible': [('show_technical_fields', '=', False)]}">
                                <group string="Technical Request Details">
                                    <field name="is_technical" invisible="1"/>
                                </group>
                            </group>
                        </page>

                        <page string="Notes" name="notes">
                            <group>
                                <field name="rejection_reason" attrs="{'invisible': [('state', '!=', 'rejected')]}" readonly="1"/>
                                <field name="completion_notes" attrs="{'invisible': [('state', 'not in', ['in_progress', 'completed'])], 'readonly': [('state', '=', 'completed')]}"/>
                            </group>
                        </page>

                        <page string="سجل الحركات" name="activity_log" attrs="{'invisible': [('id', '=', False)]}">
                            <field name="activity_log_ids" readonly="1">
                                <tree string="Activity Log" create="false" edit="false" delete="false">
                                    <field name="activity_date"/>
                                    <field name="activity_type"/>
                                    <field name="user_id"/>
                                    <field name="employee_id"/>
                                    <field name="old_state"/>
                                    <field name="new_state"/>
                                    <field name="notes"/>
                                    <field name="assigned_to_id" attrs="{'invisible': [('assigned_to_id', '=', False)]}"/>
                                    <field name="rejection_reason" attrs="{'invisible': [('rejection_reason', '=', False)]}"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Request Tree View -->
    <record id="view_bssic_request_tree" model="ir.ui.view">
        <field name="name">bssic.request.tree</field>
        <field name="model">bssic.request</field>
        <field name="arch" type="xml">
            <tree string="Requests" decoration-info="state == 'draft'" decoration-muted="state == 'rejected'" decoration-success="state == 'completed'">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="request_type_id"/>
                <field name="request_date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Request Search View -->
    <record id="view_bssic_request_search" model="ir.ui.view">
        <field name="name">bssic.request.search</field>
        <field name="model">bssic.request</field>
        <field name="arch" type="xml">
            <search string="Search Requests">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="employee_number"/>
                <field name="department_id"/>
                <field name="request_type_id"/>
                <separator/>
                <filter string="My Requests" name="my_requests" domain="[('employee_id.user_id', '=', uid)]"/>
                <filter string="My Team's Requests" name="my_team_requests" domain="[('employee_id.parent_id.user_id', '=', uid)]"/>
                <filter string="Assigned to Me" name="assigned_to_me" domain="[('assigned_to.user_id', '=', uid)]"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Submitted" name="submitted" domain="[('state', '=', 'submitted')]"/>
                <filter string="Direct Manager Approval" name="direct_manager" domain="[('state', '=', 'direct_manager')]"/>
                <filter string="Audit Manager Approval" name="audit_manager" domain="[('state', '=', 'audit_manager')]"/>
                <filter string="IT Manager Approval" name="it_manager" domain="[('state', '=', 'it_manager')]"/>
                <filter string="Assigned" name="assigned" domain="[('state', '=', 'assigned')]"/>
                <filter string="In Progress" name="in_progress" domain="[('state', '=', 'in_progress')]"/>
                <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                <filter string="Rejected" name="rejected" domain="[('state', '=', 'rejected')]"/>
                <group expand="0" string="Group By">
                    <filter string="Employee" name="employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Department" name="department" context="{'group_by': 'department_id'}"/>
                    <filter string="Request Type" name="request_type" context="{'group_by': 'request_type_id'}"/>
                    <filter string="Status" name="status" context="{'group_by': 'state'}"/>
                    <filter string="Request Date" name="request_date" context="{'group_by': 'request_date'}"/>
                    <filter string="Assigned To" name="assigned_to" context="{'group_by': 'assigned_to'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Dynamic Form View for All Requests - chooses appropriate view based on request type -->
    <record id="view_bssic_request_form_dynamic" model="ir.ui.view">
        <field name="name">bssic.request.form.dynamic</field>
        <field name="model">bssic.request</field>
        <field name="inherit_id" ref="view_bssic_request_form"/>
        <field name="arch" type="xml">
            <!-- Override header to show appropriate buttons based on request type -->
            <xpath expr="//header" position="replace">
                <header>
                    <!-- Permission Request Buttons -->
                    <button name="action_submit" string="Submit" type="object" class="oe_highlight"
                            states="draft" attrs="{'invisible': [('show_permission_fields', '=', False)]}"/>
                    <button name="action_approve_direct_manager" string="Approve (Direct Manager)" type="object" class="oe_highlight"
                            states="direct_manager" groups="bssic_requests.group_bssic_direct_manager"
                            attrs="{'invisible': [('show_permission_fields', '=', False)]}"/>
                    <button name="action_approve_audit_manager" string="Approve (Audit Manager)" type="object" class="oe_highlight"
                            states="audit_manager" groups="bssic_requests.group_bssic_audit_manager"
                            attrs="{'invisible': [('show_permission_fields', '=', False)]}"/>
                    <button name="action_approve_it_manager" string="Approve (IT Manager)" type="object" class="oe_highlight"
                            states="it_manager" groups="bssic_requests.group_bssic_it_manager"
                            attrs="{'invisible': [('show_permission_fields', '=', False)]}"/>
                    <button name="action_assign" string="Assign to IT Staff" type="object" class="oe_highlight"
                            states="assigned" groups="bssic_requests.group_bssic_it_manager"
                            attrs="{'invisible': [('show_permission_fields', '=', False)]}"/>
                    <button name="action_complete" string="Mark as Completed" type="object" class="oe_highlight"
                            states="in_progress" groups="bssic_requests.group_bssic_it_staff"
                            attrs="{'invisible': [('show_permission_fields', '=', False)]}"/>

                    <!-- Default Buttons for other request types -->
                    <button name="action_submit" string="Submit" type="object" class="oe_highlight"
                            states="draft" attrs="{'invisible': [('show_permission_fields', '=', True)]}"/>
                    <button name="action_approve_direct_manager" string="Approve (Direct Manager)" type="object" class="oe_highlight"
                            states="direct_manager" groups="bssic_requests.group_bssic_direct_manager"
                            attrs="{'invisible': [('show_permission_fields', '=', True)]}"/>
                    <button name="action_approve_it_manager" string="Approve (IT Manager)" type="object" class="oe_highlight"
                            states="it_manager" groups="bssic_requests.group_bssic_it_manager"
                            attrs="{'invisible': [('show_permission_fields', '=', True)]}"/>
                    <button name="action_assign" string="Assign to IT Staff" type="object" class="oe_highlight"
                            states="assigned" groups="bssic_requests.group_bssic_it_manager"
                            attrs="{'invisible': [('show_permission_fields', '=', True)]}"/>
                    <button name="action_complete" string="Mark as Completed" type="object" class="oe_highlight"
                            states="in_progress" groups="bssic_requests.group_bssic_it_staff"
                            attrs="{'invisible': [('show_permission_fields', '=', True)]}"/>

                    <!-- Common Reject Button -->
                    <button name="action_reject" string="Reject" type="object" class="btn-danger"
                            states="direct_manager,audit_manager,it_manager,assigned,in_progress"/>

                    <field name="is_technical" invisible="1"/>
                    <field name="show_permission_fields" invisible="1"/>
                    <field name="show_authorization_delegation_fields" invisible="1"/>
                    <field name="show_free_entry_fields" invisible="1"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submitted,direct_manager,audit_manager,it_manager,assigned,in_progress,completed"/>
                </header>
            </xpath>
        </field>
    </record>

    <!-- Main Request Action for Employees - Shows all their own requests except technical requests -->
    <record id="action_bssic_request" model="ir.actions.act_window">
        <field name="name">My Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('employee_id.user_id', '=', uid), ('request_type_id.code', '!=', 'technical')]</field>
        <!-- Remove the context filter and disable create -->
        <field name="context">{'create': False}</field>
        <field name="view_ids" eval="[(5, 0, 0), (0, 0, {'view_mode': 'tree', 'view_id': ref('view_bssic_request_tree')}), (0, 0, {'view_mode': 'form', 'view_id': ref('view_bssic_request_form_dynamic')})]"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Select a request type from the menu to create a new request
            </p>
        </field>
    </record>

    <!-- Action for Direct Managers to see their team's requests only -->
    <record id="action_bssic_request_direct_manager" model="ir.actions.act_window">
        <field name="name">Team Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '!=', 'technical'), ('employee_id.parent_id.user_id', '=', uid)]</field>
        <field name="context">{'create': False}</field>
        <field name="view_ids" eval="[(5, 0, 0), (0, 0, {'view_mode': 'tree', 'view_id': ref('view_bssic_request_tree')}), (0, 0, {'view_mode': 'form', 'view_id': ref('view_bssic_request_form_dynamic')})]"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No requests from your team members yet
            </p>
            <p>
                This view shows requests from employees who report directly to you.
            </p>
        </field>
    </record>

    <!-- Action for Audit/IT Managers and IT Staff to see all requests -->
    <record id="action_bssic_request_manager" model="ir.actions.act_window">
        <field name="name">All Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '!=', 'technical')]</field>
        <field name="context">{'create': False}</field>
        <field name="view_ids" eval="[(5, 0, 0), (0, 0, {'view_mode': 'tree', 'view_id': ref('view_bssic_request_tree')}), (0, 0, {'view_mode': 'form', 'view_id': ref('view_bssic_request_form_dynamic')})]"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Select a request type from the menu to create a new request
            </p>
        </field>
    </record>

    <!-- Actions for specific request types -->
    <record id="action_bssic_password_reset_request" model="ir.actions.act_window">
        <field name="name">Password Reset Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'password_reset')]</field>
        <field name="context">{'default_request_type_code': 'password_reset', 'form_view_ref': 'bssic_requests.view_bssic_request_form', 'default_name': 'Password Reset Request'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new password reset request
            </p>
        </field>
    </record>

    <record id="action_bssic_usb_request" model="ir.actions.act_window">
        <field name="name">USB Access Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'usb')]</field>
        <field name="context">{'default_request_type_code': 'usb', 'form_view_ref': 'bssic_requests.view_bssic_request_form', 'default_name': 'USB Access Request'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new USB access request
            </p>
        </field>
    </record>

    <record id="action_bssic_extension_request" model="ir.actions.act_window">
        <field name="name">Extension Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'extension')]</field>
        <field name="context">{'default_request_type_code': 'extension', 'form_view_ref': 'bssic_requests.view_bssic_request_form', 'default_name': 'Extension Request'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new extension request
            </p>
        </field>
    </record>

    <record id="action_bssic_permission_request" model="ir.actions.act_window">
        <field name="name">Permission Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'permission')]</field>
        <field name="context">{'default_request_type_code': 'permission', 'form_view_ref': 'bssic_requests.view_bssic_permission_request_form_new', 'default_name': 'Permission Request'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new permission request
            </p>
        </field>
    </record>

    <record id="action_bssic_email_request" model="ir.actions.act_window">
        <field name="name">Email Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'email')]</field>
        <field name="context">{'default_request_type_code': 'email', 'form_view_ref': 'bssic_requests.view_bssic_request_form', 'default_name': 'Email Request'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new email request
            </p>
        </field>
    </record>

    <record id="action_bssic_technical_request" model="ir.actions.act_window">
        <field name="name">Technical Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'technical')]</field>
        <field name="context">{'default_request_type_code': 'technical', 'default_is_technical': True, 'default_request_nature': 'technical', 'form_view_ref': 'bssic_requests.view_bssic_technical_request_form_new', 'default_name': 'Technical Request', 'allow_employee_selection': True}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new technical request
            </p>
        </field>
    </record>

    <!-- Root Menu - تأكد من أن هذا التعريف موجود مرة واحدة فقط -->
    <menuitem id="menu_bssic_root" name="BSIC Requests" web_icon="bssic_requests,static/description/icon.png" sequence="10"/>

    <!-- Menu Items -->
    <menuitem id="menu_bssic_request" name="Requests" parent="menu_bssic_root" sequence="10"/>
    <menuitem id="menu_bssic_my_request" name="My Requests" parent="menu_bssic_request" action="action_bssic_request" sequence="10"/>

    <!-- Team Requests for Direct Managers -->
    <menuitem id="menu_bssic_team_request" name="Team Requests" parent="menu_bssic_request" action="action_bssic_request_direct_manager" sequence="20" groups="bssic_requests.group_bssic_direct_manager"/>

    <!-- All Requests for Audit/IT Managers and IT Staff -->
    <menuitem id="menu_bssic_all_request" name="All Requests" parent="menu_bssic_request" action="action_bssic_request_manager" sequence="25" groups="bssic_requests.group_bssic_audit_manager,bssic_requests.group_bssic_it_manager,bssic_requests.group_bssic_it_staff"/>

    <!-- Configuration Menu -->
    <menuitem id="menu_bssic_configuration" name="Configuration" parent="menu_bssic_root" sequence="100" groups="bssic_requests.group_bssic_it_manager"/>

    <!-- Request Type Specific Menus -->
    <menuitem id="menu_bssic_password_reset_request" name="Password Reset" parent="menu_bssic_request" action="action_bssic_password_reset_request" sequence="30"/>
    <menuitem id="menu_bssic_usb_request" name="USB Access" parent="menu_bssic_request" action="action_bssic_usb_request" sequence="40"/>
    <menuitem id="menu_bssic_extension_request" name="Extension Request" parent="menu_bssic_request" action="action_bssic_extension_request" sequence="50"/>
    <menuitem id="menu_bssic_permission_request" name="Permission Request" parent="menu_bssic_request" action="action_bssic_permission_request" sequence="60"/>
    <menuitem id="menu_bssic_email_request" name="Email Request" parent="menu_bssic_request" action="action_bssic_email_request" sequence="70"/>

    <!-- Request Reject Wizard Form -->
    <record id="view_bssic_request_reject_wizard_form" model="ir.ui.view">
        <field name="name">bssic.request.reject.wizard.form</field>
        <field name="model">bssic.request.reject.wizard</field>
        <field name="arch" type="xml">
            <form string="Reject Request">
                <group>
                    <field name="request_id" invisible="1"/>
                    <field name="rejection_reason" required="1" placeholder="Please provide a reason for rejection..."/>
                </group>
                <footer>
                    <button name="action_confirm_reject" string="Confirm" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
                 </form>
                 </field>
                 </record>
                 </odoo>