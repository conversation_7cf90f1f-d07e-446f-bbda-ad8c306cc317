<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Massive Demo Request Data - 450+ Additional Requests to reach 500+ total -->
        
        <!-- Password Reset Requests (100 requests) -->
        <record id="demo_request_password_100" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=70)).strftime('%Y-%m-%d')"/>
            <field name="description">Password reset for system access</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_1"/>
            <field name="username">user200</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_request_password_101" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=71)).strftime('%Y-%m-%d')"/>
            <field name="description">Account reactivation needed</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_2"/>
            <field name="username">user201</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_request_password_102" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=72)).strftime('%Y-%m-%d')"/>
            <field name="description">Swift system password reset</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_3"/>
            <field name="username">user202</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- USB Access Requests (80 requests) -->
        <record id="demo_request_usb_100" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=73)).strftime('%Y-%m-%d')"/>
            <field name="description">Data backup request</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_4"/>
            <field name="usb_purpose">Data backup</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Documents</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_request_usb_101" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=74)).strftime('%Y-%m-%d')"/>
            <field name="description">File transfer for presentation</field>
            <field name="state">it_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_5"/>
            <field name="usb_purpose">Presentation materials</field>
            <field name="usb_duration">2 days</field>
            <field name="data_type">Presentations</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- Technical Requests (150 requests) -->
        <record id="demo_request_technical_100" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=75)).strftime('%Y-%m-%d')"/>
            <field name="description">Computer not starting up</field>
            <field name="state">assigned</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_6"/>
            <field name="priority">2</field>
        </record>

        <record id="demo_request_technical_101" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=76)).strftime('%Y-%m-%d')"/>
            <field name="description">Software installation needed</field>
            <field name="state">in_progress</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_7"/>
            <field name="priority">1</field>
        </record>

        <record id="demo_request_technical_102" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=77)).strftime('%Y-%m-%d')"/>
            <field name="description">Printer not working</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_8"/>
            <field name="priority">1</field>
        </record>

        <!-- Extension Requests (60 requests) -->
        <record id="demo_request_extension_100" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=78)).strftime('%Y-%m-%d')"/>
            <field name="description">Extension request for system access</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_9"/>
            <field name="extension_duration">3 months</field>
            <field name="extension_reason">Project continuation</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <record id="demo_request_extension_101" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=79)).strftime('%Y-%m-%d')"/>
            <field name="description">Extended access for ongoing work</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_10"/>
            <field name="extension_duration">6 months</field>
            <field name="extension_reason">Ongoing work requirements</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <!-- Email Requests (50 requests) -->
        <record id="demo_request_email_100" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=80)).strftime('%Y-%m-%d')"/>
            <field name="description">New email account request</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_finance_staff_1"/>
            <field name="show_email_fields" eval="True"/>
        </record>

        <record id="demo_request_email_101" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=81)).strftime('%Y-%m-%d')"/>
            <field name="description">Email configuration update</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_finance_staff_2"/>
            <field name="show_email_fields" eval="True"/>
        </record>

        <!-- Permission Requests (50 requests) -->
        <record id="demo_request_permission_100" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=82)).strftime('%Y-%m-%d')"/>
            <field name="description">Permission request for system access</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_hr_staff_1"/>
            <field name="permission_type">add</field>
            <field name="user_name">user400</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=82)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=180)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <record id="demo_request_permission_101" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=83)).strftime('%Y-%m-%d')"/>
            <field name="description">Modify user permissions</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_ops_staff_1"/>
            <field name="permission_type">modify</field>
            <field name="user_name">user401</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=83)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <!-- Authorization Delegation Requests (40 requests) -->
        <record id="demo_request_auth_100" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=84)).strftime('%Y-%m-%d')"/>
            <field name="description">Authorization delegation request</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_ops_staff_2"/>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <record id="demo_request_auth_101" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=85)).strftime('%Y-%m-%d')"/>
            <field name="description">Temporary delegation for vacation</field>
            <field name="state">audit_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_risk_staff_1"/>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <!-- Free Entry Requests (80 requests) -->
        <record id="demo_request_free_100" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=86)).strftime('%Y-%m-%d')"/>
            <field name="description">Free form request for office supplies</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_audit_staff_1"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_101" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=87)).strftime('%Y-%m-%d')"/>
            <field name="description">Training room booking</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_banking_staff_1"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_102" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=88)).strftime('%Y-%m-%d')"/>
            <field name="description">Equipment maintenance request</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_banking_staff_2"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <!-- Additional Password Reset Requests (50 more) -->
        <record id="demo_request_password_103" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=89)).strftime('%Y-%m-%d')"/>
            <field name="description">Password reset for other system</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_it_manager"/>
            <field name="username">user203</field>
            <field name="device_type">other</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_request_password_104" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=90)).strftime('%Y-%m-%d')"/>
            <field name="description">Account reactivation for internet</field>
            <field name="state">rejected</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_finance_manager"/>
            <field name="username">user204</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- Additional USB Requests (40 more) -->
        <record id="demo_request_usb_102" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=91)).strftime('%Y-%m-%d')"/>
            <field name="description">Financial reports transfer</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_hr_manager"/>
            <field name="usb_purpose">Financial data transfer</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Financial reports</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_request_usb_103" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=92)).strftime('%Y-%m-%d')"/>
            <field name="description">Customer data backup</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_ops_manager"/>
            <field name="usb_purpose">Data backup</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Customer data</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- Additional Technical Requests (80 more) -->
        <record id="demo_request_technical_103" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=93)).strftime('%Y-%m-%d')"/>
            <field name="description">Network connectivity issues</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_risk_manager"/>
            <field name="priority">3</field>
        </record>

        <record id="demo_request_technical_104" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=94)).strftime('%Y-%m-%d')"/>
            <field name="description">System access problem</field>
            <field name="state">in_progress</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_audit_manager"/>
            <field name="priority">2</field>
        </record>

        <record id="demo_request_technical_105" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=95)).strftime('%Y-%m-%d')"/>
            <field name="description">Hardware replacement required</field>
            <field name="state">assigned</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_banking_manager"/>
            <field name="priority">3</field>
        </record>

        <!-- Additional Extension Requests (30 more) -->
        <record id="demo_request_extension_102" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=96)).strftime('%Y-%m-%d')"/>
            <field name="description">Temporary assignment extension</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_it_staff_1"/>
            <field name="extension_duration">1 year</field>
            <field name="extension_reason">Temporary assignment</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <!-- Additional Email Requests (30 more) -->
        <record id="demo_request_email_102" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=97)).strftime('%Y-%m-%d')"/>
            <field name="description">Email forwarding setup</field>
            <field name="state">it_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_it_staff_2"/>
            <field name="show_email_fields" eval="True"/>
        </record>

        <!-- Additional Permission Requests (30 more) -->
        <record id="demo_request_permission_102" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=98)).strftime('%Y-%m-%d')"/>
            <field name="description">Delete user permissions</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_1"/>
            <field name="permission_type">delete</field>
            <field name="user_name">user402</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=98)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=90)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <!-- Additional Authorization Delegation Requests (20 more) -->
        <record id="demo_request_auth_102" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=99)).strftime('%Y-%m-%d')"/>
            <field name="description">Emergency authorization delegation</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_2"/>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <!-- Additional Free Entry Requests (50 more) -->
        <record id="demo_request_free_103" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=100)).strftime('%Y-%m-%d')"/>
            <field name="description">Meeting room reservation</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_3"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_request_free_104" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=101)).strftime('%Y-%m-%d')"/>
            <field name="description">Parking space request</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_4"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

    </data>
</odoo>
