#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to generate bulk demo request data for BSSIC Requests module
This script generates XML records for demo purposes
"""

import random

# Request types and their configurations
REQUEST_TYPES = {
    'password_reset': {
        'ref': 'request_type_password_reset',
        'device_types': ['internet', 'system', 'swift', 'other'],
        'request_reasons': ['password_reset', 'account_reactivation'],
        'descriptions': [
            'Password reset for system access',
            'Account locked - reactivation needed',
            'Forgotten password for internet access',
            'Swift system password reset',
            'System login issues'
        ]
    },
    'usb': {
        'ref': 'request_type_usb',
        'usb_purposes': ['Data backup', 'File transfer', 'Document sharing', 'Presentation materials', 'Report distribution'],
        'usb_durations': ['1 day', '2 days', '3 days', '1 week', '2 weeks'],
        'data_types': ['Documents', 'Presentations', 'Reports', 'Financial data', 'Customer data'],
        'descriptions': [
            'USB access for data backup',
            'File transfer for presentation',
            'Financial reports transfer',
            'Customer data backup',
            'Document sharing with external party'
        ]
    },
    'technical': {
        'ref': 'request_type_technical',
        'priorities': ['0', '1', '2', '3'],
        'descriptions': [
            'Computer not starting up',
            'Software installation needed',
            'Printer not working',
            'Network connectivity issues',
            'System access problem',
            'Hardware replacement required',
            'Software update needed',
            'Data recovery request',
            'Email configuration issue',
            'VPN access problem',
            'Monitor display issues',
            'Keyboard/mouse malfunction',
            'Application crashes frequently',
            'Slow system performance',
            'File sharing problems'
        ]
    },
    'extension': {
        'ref': 'request_type_extension',
        'extension_durations': ['1 month', '3 months', '6 months', '1 year'],
        'extension_reasons': ['Project continuation', 'Ongoing work requirements', 'Extended responsibilities', 'Temporary assignment'],
        'descriptions': [
            'Extension request for system access',
            'Extended access for ongoing work',
            'Temporary assignment extension',
            'Project continuation access',
            'Extended responsibilities access'
        ]
    },
    'email': {
        'ref': 'request_type_email',
        'descriptions': [
            'New email account request',
            'Email configuration update',
            'Email forwarding setup',
            'Distribution list access',
            'Email signature setup'
        ]
    },
    'permission': {
        'ref': 'request_type_permission',
        'permission_types': ['add', 'modify', 'delete', 'withdraw', 'activate', 'deactivate'],
        'descriptions': [
            'Permission request for system access',
            'Modify user permissions',
            'Delete user permissions',
            'Activate user account',
            'Deactivate user access',
            'Withdraw user permissions'
        ]
    },
    'authorization_delegation': {
        'ref': 'request_type_authorization_delegation',
        'descriptions': [
            'Authorization delegation request',
            'Temporary delegation for vacation',
            'Emergency authorization delegation',
            'Project-based delegation'
        ]
    },
    'free_entry': {
        'ref': 'request_type_free_entry',
        'descriptions': [
            'Free form request for office supplies',
            'Training room booking',
            'Equipment maintenance request',
            'Meeting room reservation',
            'Parking space request',
            'Building access card',
            'Visitor access arrangement'
        ]
    }
}

# Request states
STATES = ['draft', 'submitted', 'direct_manager', 'audit_manager', 'it_manager', 'assigned', 'in_progress', 'completed', 'rejected']

def generate_request_xml(request_id, request_type, index):
    """Generate XML for a single request"""
    config = REQUEST_TYPES[request_type]
    
    xml = f'        <record id="demo_bulk_{request_type}_{index:03d}" model="bssic.request">\n'
    xml += f'            <field name="request_type_id" ref="{config["ref"]}"/>\n'
    xml += f'            <field name="request_type_code">{request_type}</field>\n'
    xml += f'            <field name="request_date" eval="(DateTime.now() - timedelta(days={random.randint(1, 90)})).strftime(\'%Y-%m-%d\')"/>\n'
    xml += f'            <field name="description">{random.choice(config["descriptions"])}</field>\n'
    xml += f'            <field name="state">{random.choice(STATES)}</field>\n'
    
    # Add specific fields based on request type
    if request_type == 'password_reset':
        xml += f'            <field name="username">user{random.randint(100, 999):03d}</field>\n'
        xml += f'            <field name="device_type">{random.choice(config["device_types"])}</field>\n'
        xml += f'            <field name="request_reason">{random.choice(config["request_reasons"])}</field>\n'
        xml += f'            <field name="show_password_fields" eval="True"/>\n'
    
    elif request_type == 'usb':
        xml += f'            <field name="usb_purpose">{random.choice(config["usb_purposes"])}</field>\n'
        xml += f'            <field name="usb_duration">{random.choice(config["usb_durations"])}</field>\n'
        xml += f'            <field name="data_type">{random.choice(config["data_types"])}</field>\n'
        xml += f'            <field name="show_usb_fields" eval="True"/>\n'
    
    elif request_type == 'extension':
        xml += f'            <field name="extension_duration">{random.choice(config["extension_durations"])}</field>\n'
        xml += f'            <field name="extension_reason">{random.choice(config["extension_reasons"])}</field>\n'
        xml += f'            <field name="show_extension_fields" eval="True"/>\n'
    
    elif request_type == 'technical':
        xml += f'            <field name="priority">{random.choice(config["priorities"])}</field>\n'
    
    elif request_type == 'email':
        xml += f'            <field name="show_email_fields" eval="True"/>\n'
    
    elif request_type == 'permission':
        xml += f'            <field name="permission_type">{random.choice(config["permission_types"])}</field>\n'
        xml += f'            <field name="user_name">user{random.randint(100, 999):03d}</field>\n'
        xml += f'            <field name="validity_from" eval="(DateTime.now() - timedelta(days={random.randint(1, 30)})).strftime(\'%Y-%m-%d\')"/>\n'
        xml += f'            <field name="validity_to" eval="(DateTime.now() + timedelta(days={random.randint(30, 365)})).strftime(\'%Y-%m-%d\')"/>\n'
        xml += f'            <field name="show_permission_fields" eval="True"/>\n'
    
    elif request_type == 'authorization_delegation':
        xml += f'            <field name="show_authorization_delegation_fields" eval="True"/>\n'
    
    elif request_type == 'free_entry':
        xml += f'            <field name="show_free_entry_fields" eval="True"/>\n'
    
    xml += f'        </record>\n\n'
    return xml

def generate_bulk_requests_xml():
    """Generate XML for bulk requests"""
    xml_content = '''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Bulk Demo Request Data - 500+ Requests -->
        
'''
    
    # Generate requests for each type
    request_counts = {
        'password_reset': 100,
        'usb': 80,
        'technical': 150,
        'extension': 60,
        'email': 50,
        'permission': 50,
        'authorization_delegation': 40,
        'free_entry': 80
    }
    
    for request_type, count in request_counts.items():
        xml_content += f'        <!-- {request_type.replace("_", " ").title()} Requests ({count} requests) -->\n'
        for i in range(1, count + 1):
            xml_content += generate_request_xml(f'{request_type}_{i}', request_type, i)
    
    xml_content += '''    </data>
</odoo>'''
    
    return xml_content

if __name__ == "__main__":
    print("Generating bulk demo requests XML...")
    xml_content = generate_bulk_requests_xml()
    
    with open('request_demo_bulk_generated.xml', 'w', encoding='utf-8') as f:
        f.write(xml_content)
    
    print("Generated request_demo_bulk_generated.xml with 610 demo requests!")
    print("Request counts:")
    print("- Password Reset: 100")
    print("- USB Access: 80") 
    print("- Technical: 150")
    print("- Extension: 60")
    print("- Email: 50")
    print("- Permission: 50")
    print("- Authorization Delegation: 40")
    print("- Free Entry: 80")
    print("Total: 610 requests")
