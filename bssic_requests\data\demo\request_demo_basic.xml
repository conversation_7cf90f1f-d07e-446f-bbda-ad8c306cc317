<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Basic Demo Request Data - Simple requests without complex references -->
        
        <!-- Technical Request - Simplest type -->
        <record id="demo_basic_technical_1" model="bssic.request">
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="description">Computer not starting up</field>
            <field name="state">submitted</field>
            <field name="priority">2</field>
        </record>

        <record id="demo_basic_technical_2" model="bssic.request">
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="description">Software installation needed</field>
            <field name="state">in_progress</field>
            <field name="priority">1</field>
        </record>

        <record id="demo_basic_technical_3" model="bssic.request">
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="description">Printer not working</field>
            <field name="state">completed</field>
            <field name="priority">1</field>
        </record>

        <record id="demo_basic_technical_4" model="bssic.request">
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="description">Network connectivity issues</field>
            <field name="state">assigned</field>
            <field name="priority">3</field>
        </record>

        <record id="demo_basic_technical_5" model="bssic.request">
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="description">System access problem</field>
            <field name="state">direct_manager</field>
            <field name="priority">2</field>
        </record>

        <!-- Password Reset Requests -->
        <record id="demo_basic_password_1" model="bssic.request">
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="description">Password reset for system access</field>
            <field name="state">submitted</field>
            <field name="username">user001</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_basic_password_2" model="bssic.request">
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="description">Account reactivation needed</field>
            <field name="state">completed</field>
            <field name="username">user002</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- USB Access Requests -->
        <record id="demo_basic_usb_1" model="bssic.request">
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="description">USB access for data backup</field>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data backup</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Documents</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_basic_usb_2" model="bssic.request">
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="description">File transfer for presentation</field>
            <field name="state">completed</field>
            <field name="usb_purpose">Presentation materials</field>
            <field name="usb_duration">2 days</field>
            <field name="data_type">Presentations</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- Extension Requests -->
        <record id="demo_basic_extension_1" model="bssic.request">
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="description">Extension request for system access</field>
            <field name="state">completed</field>
            <field name="extension_duration">3 months</field>
            <field name="extension_reason">Project continuation</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <!-- Email Requests -->
        <record id="demo_basic_email_1" model="bssic.request">
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="description">New email account request</field>
            <field name="state">completed</field>
            <field name="show_email_fields" eval="True"/>
        </record>

        <!-- Permission Requests -->
        <record id="demo_basic_permission_1" model="bssic.request">
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="description">Permission request for system access</field>
            <field name="state">completed</field>
            <field name="permission_type">add</field>
            <field name="user_name">user100</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=180)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <!-- Authorization Delegation Requests -->
        <record id="demo_basic_auth_1" model="bssic.request">
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="description">Authorization delegation request</field>
            <field name="state">completed</field>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <!-- Free Entry Requests -->
        <record id="demo_basic_free_1" model="bssic.request">
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=14)).strftime('%Y-%m-%d')"/>
            <field name="description">Free form request for office supplies</field>
            <field name="state">completed</field>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <!-- Additional Technical Requests -->
        <record id="demo_basic_technical_6" model="bssic.request">
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="description">Hardware replacement required</field>
            <field name="state">submitted</field>
            <field name="priority">3</field>
        </record>

        <record id="demo_basic_technical_7" model="bssic.request">
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="description">Software update needed</field>
            <field name="state">in_progress</field>
            <field name="priority">1</field>
        </record>

        <record id="demo_basic_technical_8" model="bssic.request">
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="description">Data recovery request</field>
            <field name="state">completed</field>
            <field name="priority">3</field>
        </record>

        <record id="demo_basic_technical_9" model="bssic.request">
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="description">Email configuration issue</field>
            <field name="state">assigned</field>
            <field name="priority">2</field>
        </record>

        <record id="demo_basic_technical_10" model="bssic.request">
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="description">VPN access problem</field>
            <field name="state">direct_manager</field>
            <field name="priority">2</field>
        </record>

        <!-- Additional Password Reset Requests -->
        <record id="demo_basic_password_3" model="bssic.request">
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="description">Swift system password reset</field>
            <field name="state">direct_manager</field>
            <field name="username">user003</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="demo_basic_password_4" model="bssic.request">
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=21)).strftime('%Y-%m-%d')"/>
            <field name="description">System login issues</field>
            <field name="state">rejected</field>
            <field name="username">user004</field>
            <field name="device_type">other</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- Additional USB Access Requests -->
        <record id="demo_basic_usb_3" model="bssic.request">
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="description">Document sharing with external party</field>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Document sharing</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Reports</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_basic_usb_4" model="bssic.request">
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="description">Financial data transfer</field>
            <field name="state">it_manager</field>
            <field name="usb_purpose">Financial data transfer</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Financial data</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- Additional Extension Requests -->
        <record id="demo_basic_extension_2" model="bssic.request">
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="description">Extended access for ongoing work</field>
            <field name="state">direct_manager</field>
            <field name="extension_duration">6 months</field>
            <field name="extension_reason">Ongoing work requirements</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <!-- Additional Email Requests -->
        <record id="demo_basic_email_2" model="bssic.request">
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="description">Email configuration update</field>
            <field name="state">submitted</field>
            <field name="show_email_fields" eval="True"/>
        </record>

        <!-- Additional Permission Requests -->
        <record id="demo_basic_permission_2" model="bssic.request">
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="description">Modify user permissions</field>
            <field name="state">direct_manager</field>
            <field name="permission_type">modify</field>
            <field name="user_name">user101</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <!-- Additional Authorization Delegation Requests -->
        <record id="demo_basic_auth_2" model="bssic.request">
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=27)).strftime('%Y-%m-%d')"/>
            <field name="description">Temporary delegation for vacation</field>
            <field name="state">audit_manager</field>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <!-- Additional Free Entry Requests -->
        <record id="demo_basic_free_2" model="bssic.request">
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="description">Training room booking</field>
            <field name="state">submitted</field>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

        <record id="demo_basic_free_3" model="bssic.request">
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="description">Equipment maintenance request</field>
            <field name="state">direct_manager</field>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

    </data>
</odoo>
