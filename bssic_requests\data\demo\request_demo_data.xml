<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Request Data - Password Reset Requests -->

        <!-- Password Reset Request 1 -->
        <record id="demo_request_password_1" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="description">Password reset request for system access</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_1"/>
            <field name="username">user001</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- Password Reset Request 2 -->
        <record id="demo_request_password_2" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="description">Account reactivation needed</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_2"/>
            <field name="username">user002</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- Password Reset Request 3 -->
        <record id="demo_request_password_3" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="description">Swift system password reset</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_3"/>
            <field name="username">user003</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <!-- USB Access Requests -->
        <record id="demo_request_usb_1" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="description">USB access request for data transfer</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_4"/>
            <field name="usb_purpose">Data backup</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Documents</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="demo_request_usb_2" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="description">File transfer for presentation</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_5"/>
            <field name="usb_purpose">Presentation materials</field>
            <field name="usb_duration">2 days</field>
            <field name="data_type">Presentations</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <!-- Extension Requests -->
        <record id="demo_request_extension_1" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="description">Extension request for system access</field>
            <field name="state">it_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_6"/>
            <field name="extension_duration">3 months</field>
            <field name="extension_reason">Project continuation</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <record id="demo_request_extension_2" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="description">Extended access for ongoing work</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_7"/>
            <field name="extension_duration">6 months</field>
            <field name="extension_reason">Ongoing work requirements</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <!-- Technical Requests -->
        <record id="demo_request_technical_1" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="description">Computer not starting up</field>
            <field name="state">assigned</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_8"/>
            <field name="priority">2</field>
        </record>

        <record id="demo_request_technical_2" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="description">Software installation needed</field>
            <field name="state">in_progress</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_9"/>
            <field name="priority">1</field>
        </record>

        <record id="demo_request_technical_3" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="description">Printer not working</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_10"/>
            <field name="priority">1</field>
        </record>

        <!-- Permission Requests -->
        <record id="demo_request_permission_1" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="description">Permission request for system access</field>
            <field name="state">direct_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_1"/>
            <field name="permission_type">add</field>
            <field name="user_name">user123</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=180)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">user</field>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <record id="demo_request_permission_2" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="description">Modify user permissions</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_2"/>
            <field name="permission_type">modify</field>
            <field name="user_name">user456</field>
            <field name="validity_from" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">user</field>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <!-- Email Requests -->
        <record id="demo_request_email_1" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="description">New email account request</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_3"/>
            <field name="show_email_fields" eval="True"/>
        </record>

        <record id="demo_request_email_2" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="description">Email configuration update</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_staff_4"/>
            <field name="show_email_fields" eval="True"/>
        </record>

        <!-- More Technical Requests -->
        <record id="demo_request_technical_4" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="description">Network connectivity issues</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_finance_staff_1"/>
            <field name="priority">3</field>
        </record>

        <record id="demo_request_technical_5" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="description">Hardware replacement required</field>
            <field name="state">assigned</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_finance_staff_2"/>
            <field name="priority">2</field>
        </record>

        <record id="demo_request_technical_6" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=14)).strftime('%Y-%m-%d')"/>
            <field name="description">Software update needed</field>
            <field name="state">completed</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_hr_staff_1"/>
            <field name="priority">1</field>
        </record>

        <!-- Authorization Delegation Requests -->
        <record id="demo_request_auth_1" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="description">Authorization delegation request</field>
            <field name="state">audit_manager</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_ops_staff_1"/>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <!-- Free Entry Requests -->
        <record id="demo_request_free_1" model="bssic.request">
            <field name="request_type_id" ref="bssic_requests.request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="description">Free form request for office supplies</field>
            <field name="state">submitted</field>
            <field name="employee_id" ref="bssic_requests.hr_employee_ops_staff_2"/>
            <field name="show_free_entry_fields" eval="True"/>
        </record>

    </data>
</odoo>
