<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Request Data - Part 1: Password Reset Requests -->
        
        <!-- Password Reset Requests - First 50 -->
        <function model="bssic.request" name="create">
            <value eval="[{
                'request_type_id': ref('bssic_requests.request_type_password_reset'),
                'request_type_code': 'password_reset',
                'request_date': (datetime.now() - timedelta(days=random.randint(1, 90))).strftime('%Y-%m-%d'),
                'description': 'Password reset request for system access',
                'state': random.choice(['draft', 'submitted', 'direct_manager', 'it_manager', 'completed', 'rejected']),
                'employee_id': ref('bssic_requests.hr_employee_staff_%s' % str(random.randint(1, 10))),
                'username': 'user%s' % str(random.randint(100, 999)),
                'device_type': random.choice(['internet', 'system', 'swift', 'other']),
                'request_reason': random.choice(['password_reset', 'account_reactivation']),
                'show_password_fields': True
            } for i in range(50)]"/>
        </function>

        <!-- USB Access Requests - Next 50 -->
        <function model="bssic.request" name="create">
            <value eval="[{
                'request_type_id': ref('bssic_requests.request_type_usb'),
                'request_type_code': 'usb',
                'request_date': (datetime.now() - timedelta(days=random.randint(1, 90))).strftime('%Y-%m-%d'),
                'description': 'USB access request for data transfer',
                'state': random.choice(['draft', 'submitted', 'direct_manager', 'it_manager', 'completed', 'rejected']),
                'employee_id': ref('bssic_requests.hr_employee_staff_%s' % str(random.randint(1, 10))),
                'usb_purpose': random.choice(['Data backup', 'File transfer', 'Document sharing', 'Presentation materials']),
                'usb_duration': random.choice(['1 day', '2 days', '1 week', '2 weeks']),
                'data_type': random.choice(['Documents', 'Presentations', 'Reports', 'Financial data', 'Customer data']),
                'show_usb_fields': True
            } for i in range(50)]"/>
        </function>

        <!-- Extension Requests - Next 50 -->
        <function model="bssic.request" name="create">
            <value eval="[{
                'request_type_id': ref('bssic_requests.request_type_extension'),
                'request_type_code': 'extension',
                'request_date': (datetime.now() - timedelta(days=random.randint(1, 90))).strftime('%Y-%m-%d'),
                'description': 'Extension request for system access',
                'state': random.choice(['draft', 'submitted', 'direct_manager', 'it_manager', 'completed', 'rejected']),
                'employee_id': ref('bssic_requests.hr_employee_staff_%s' % str(random.randint(1, 10))),
                'extension_duration': random.choice(['1 month', '3 months', '6 months', '1 year']),
                'extension_reason': random.choice(['Project continuation', 'Ongoing work requirements', 'Extended responsibilities', 'Temporary assignment']),
                'show_extension_fields': True
            } for i in range(50)]"/>
        </function>

        <!-- Permission Requests - Next 50 -->
        <function model="bssic.request" name="create">
            <value eval="[{
                'request_type_id': ref('bssic_requests.request_type_permission'),
                'request_type_code': 'permission',
                'request_date': (datetime.now() - timedelta(days=random.randint(1, 90))).strftime('%Y-%m-%d'),
                'description': 'Permission request for system access',
                'state': random.choice(['draft', 'submitted', 'direct_manager', 'it_manager', 'completed', 'rejected']),
                'employee_id': ref('bssic_requests.hr_employee_staff_%s' % str(random.randint(1, 10))),
                'permission_type': random.choice(['add', 'modify', 'delete', 'withdraw', 'activate', 'deactivate']),
                'user_name': 'user%s' % str(random.randint(100, 999)),
                'validity_from': (datetime.now() - timedelta(days=random.randint(1, 30))).strftime('%Y-%m-%d'),
                'validity_to': (datetime.now() + timedelta(days=random.randint(30, 365))).strftime('%Y-%m-%d'),
                'accounting_dept': random.choice([True, False]),
                'accounting_level': 'user' if eval('accounting_dept') else False,
                'show_permission_fields': True
            } for i in range(50)]"/>
        </function>

        <!-- Email Requests - Next 50 -->
        <function model="bssic.request" name="create">
            <value eval="[{
                'request_type_id': ref('bssic_requests.request_type_email'),
                'request_type_code': 'email',
                'request_date': (datetime.now() - timedelta(days=random.randint(1, 90))).strftime('%Y-%m-%d'),
                'description': 'Email account request',
                'state': random.choice(['draft', 'submitted', 'direct_manager', 'it_manager', 'completed', 'rejected']),
                'employee_id': ref('bssic_requests.hr_employee_staff_%s' % str(random.randint(1, 10))),
                'show_email_fields': True
            } for i in range(50)]"/>
        </function>

        <!-- Technical Requests - Next 100 -->
        <function model="bssic.request" name="create">
            <value eval="[{
                'request_type_id': ref('bssic_requests.request_type_technical'),
                'request_type_code': 'technical',
                'request_date': (datetime.now() - timedelta(days=random.randint(1, 90))).strftime('%Y-%m-%d'),
                'description': random.choice([
                    'Computer not starting up',
                    'Software installation needed',
                    'Printer not working',
                    'Network connectivity issues',
                    'System access problem',
                    'Hardware replacement required',
                    'Software update needed',
                    'Data recovery request',
                    'Email configuration issue',
                    'VPN access problem'
                ]),
                'state': random.choice(['draft', 'submitted', 'direct_manager', 'it_manager', 'assigned', 'in_progress', 'completed', 'rejected']),
                'employee_id': ref('bssic_requests.hr_employee_staff_%s' % str(random.randint(1, 10))),
                'priority': random.choice(['0', '1', '2', '3'])
            } for i in range(100)]"/>
        </function>

        <!-- Authorization Delegation Requests - Next 50 -->
        <function model="bssic.request" name="create">
            <value eval="[{
                'request_type_id': ref('bssic_requests.request_type_authorization_delegation'),
                'request_type_code': 'authorization_delegation',
                'request_date': (datetime.now() - timedelta(days=random.randint(1, 90))).strftime('%Y-%m-%d'),
                'description': 'Authorization delegation request',
                'state': random.choice(['draft', 'submitted', 'direct_manager', 'audit_manager', 'completed', 'rejected']),
                'employee_id': ref('bssic_requests.hr_employee_staff_%s' % str(random.randint(1, 10))),
                'show_authorization_delegation_fields': True
            } for i in range(50)]"/>
        </function>

        <!-- Free Entry Requests - Next 100 -->
        <function model="bssic.request" name="create">
            <value eval="[{
                'request_type_id': ref('bssic_requests.request_type_free_entry'),
                'request_type_code': 'free_entry',
                'request_date': (datetime.now() - timedelta(days=random.randint(1, 90))).strftime('%Y-%m-%d'),
                'description': 'Free form request for various purposes',
                'state': random.choice(['draft', 'submitted', 'direct_manager', 'completed', 'rejected']),
                'employee_id': ref('bssic_requests.hr_employee_staff_%s' % str(random.randint(1, 10))),
                'show_free_entry_fields': True
            } for i in range(100)]"/>
        </function>

    </data>
</odoo>
